.SellerProfile {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-family: "Poppins", sans-serif;
  background: transparent;
}

.SellerProfile__container {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: flex-start;
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: 12px;
  padding: 32px 32px 24px 32px;
  box-sizing: border-box;
  margin-top: 12px;
  gap: 32px;
}

.SellerProfile__left-section {
  display: flex;
  flex-direction: column;
  gap: 18px;
  flex: 1 1 0;
  min-width: 0;
}

.SellerProfile__right-section {
  width: 320px;
  min-width: 260px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-left: 1px solid var(--light-gray);
  padding-left: 32px;
}

.SellerProfile__form-row {
  display: flex;
  gap: 18px;
  width: 100%;
}

.SellerProfile__input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0;
}

.SellerProfile__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  background-color: var(--white);
  transition: border-color 0.3s, box-shadow 0.3s;
  overflow: hidden;
  margin-top: 6px;
}

.SellerProfile__input-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.08);
}

.SellerProfile__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 14px;
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  border-right: 1px solid var(--light-gray);
  height: 100%;
  min-width: 40px;
  font-size: 18px;
}

.SellerProfile__input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  color: var(--text-color);
  background-color: transparent;
  font-family: inherit;
}

.SellerProfile__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.SellerProfile__input:focus {
  outline: none;
}

.SellerProfile__input--disabled {
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  cursor: not-allowed;
}

.SellerProfile__input--disabled::placeholder {
  color: var(--dark-gray);
  opacity: 0.5;
}

.SellerProfile__textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.SellerProfile__image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  border-radius: 16px;
  border: none;
  width: 100%;
}

.SellerProfile__image-title {
  display: flex;
  justify-content: center;
  font-size: 16px;
  color: var(--secondary-color);
  padding-bottom: 12px;
  font-weight: 500;
  margin-bottom: 0;
}

.SellerProfile__image {
  width: 96px;
  height: 96px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid var(--light-gray);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  margin-bottom: 16px;
}

.SellerProfile__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.SellerProfile__placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  font-weight: 600;
}

.SellerProfile__user-icon {
  font-size: 44px;
  color: var(--dark-gray);
}

.SellerProfile__upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: 8px 22px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 0;
}

.SellerProfile__upload-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.08);
  transform: none;
}

.SellerProfile__upload-btn:active {
  background-color: var(--btn-color);
  color: var(--white);
  box-shadow: none;
}

.SellerProfile__buttons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 18px;
  margin-top: 32px;
  width: 100%;
}

.SellerProfile__save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #ff512f 0%, #ff6e2f 100%);
  color: var(--white);
  border: none;
  padding: 12px 38px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
  font-size: 16px;
  min-width: 160px;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.08);
}

.SellerProfile__save-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #ff6e2f 0%, #ff512f 100%);
  box-shadow: 0 4px 16px rgba(255, 102, 0, 0.12);
}

.SellerProfile__save-btn:active:not(:disabled) {
  box-shadow: 0 2px 6px rgba(255, 102, 0, 0.1);
}

.SellerProfile__save-btn:disabled {
  background: var(--dark-gray);
  cursor: not-allowed;
  opacity: 0.6;
}

.SellerProfile__delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
  padding: 12px 38px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 16px;
  min-width: 160px;
}

.SellerProfile__delete-btn:hover {
  background-color: var(--danger-color);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.1);
}

.SellerProfile__delete-btn:active {
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.08);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .SellerProfile__container {
    flex-direction: column;
    align-items: stretch;
    padding: 24px 12px 18px 12px;
    gap: 24px;
  }
  .SellerProfile__right-section {
    width: 100%;
    min-width: 0;
    border-left: none;
    border-top: 1px solid var(--light-gray);
    padding-left: 0;
    padding-top: 24px;
    margin-bottom: 0;
    order: 2;
  }
  .SellerProfile__left-section {
    width: 100%;
    gap: 16px;
    order: 1;
  }
}

@media (max-width: 768px) {
  .SellerProfile__container {
    flex-direction: column;
    align-items: stretch;
    padding: 16px 4px 12px 4px;
    gap: 18px;
  }
  .SellerProfile__right-section {
    width: 100%;
    min-width: 0;
    border-left: none;
    border-top: 1px solid var(--light-gray);
    padding-left: 0;
    padding-top: 18px;
    margin-bottom: 0;
    order: 2;
  }
  .SellerProfile__left-section {
    width: 100%;
    gap: 12px;
    order: 1;
  }
  .SellerProfile__form-row {
    flex-direction: column;
    gap: 12px;
  }
  .SellerProfile__buttons {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }
  .SellerProfile__save-btn,
  .SellerProfile__delete-btn {
    width: 100%;
    min-width: 0;
    padding-left: 0;
    padding-right: 0;
  }
}
