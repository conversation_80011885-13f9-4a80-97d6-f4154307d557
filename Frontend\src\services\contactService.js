import api from './api';
import { CMS_ENDPOINTS } from '../utils/constants';

/**
 * Submit contact form
 * @param {Object} contactData - Contact form data
 * @param {string} contactData.firstName - First name
 * @param {string} contactData.lastName - Last name
 * @param {string} contactData.email - Email address
 * @param {string} contactData.mobile - Mobile number
 * @param {string} contactData.message - Message content
 * @returns {Promise} Promise with submission result
 */
export const submitContactForm = async (contactData) => {
  const response = await api.post(CMS_ENDPOINTS.CONTACT, contactData);
  return response.data;
};

export default {
  submitContactForm,
};
